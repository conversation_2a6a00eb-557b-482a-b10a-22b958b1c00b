/* Scroll Progress Indicator - Professional Legal Aesthetic */
.scroll-progress-indicator {
  position: fixed;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  z-index: 10000;
  pointer-events: none;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.scroll-progress-indicator.hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.scroll-progress-indicator.visible {
  opacity: 1;
  visibility: visible;
}

/* Specific positioning for different sections */
.homepage2 .scroll-progress-indicator {
  right: 2rem;
}

.founding-member-perks-section .scroll-progress-indicator {
  right: 2rem;
}

/* Pill-shaped container */
.scroll-progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid #966f33;
  border-radius: 2rem;
  box-shadow: 0 4px 20px rgba(150, 111, 51, 0.15);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

/* Individual progress circles */
.scroll-progress-circle {
  width: 1rem;
  height: 1rem;
  border: 2px solid #966f33;
  border-radius: 50%;
  background: transparent;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  pointer-events: auto;
}

/* Inner circle for fill effect */
.scroll-progress-circle-inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: transparent;
  transform: scale(0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Active state - filled circle */
.scroll-progress-circle.active {
  background: #966f33;
  border-color: #966f33;
  transform: scale(1.1);
}

.scroll-progress-circle.active .scroll-progress-circle-inner {
  background: #966f33;
  transform: scale(1);
}

/* Hover effects */
.scroll-progress-circle:hover {
  transform: scale(1.2);
  border-color: #b99c6d;
  box-shadow: 0 0 0 4px rgba(150, 111, 51, 0.2);
}

.scroll-progress-circle.active:hover {
  background: #b99c6d;
  border-color: #b99c6d;
}

/* Click/Active state */
.scroll-progress-circle:active {
  transform: scale(1.05);
  transition: transform 0.1s ease;
}

/* Focus state for keyboard navigation */
.scroll-progress-circle:focus {
  outline: 2px solid #966f33;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(150, 111, 51, 0.3);
}

/* Container hover effect */
.scroll-progress-container:hover {
  background: rgba(255, 255, 255, 0.98);
  border-color: #b99c6d;
  box-shadow: 0 6px 30px rgba(150, 111, 51, 0.25);
  transform: scale(1.05);
}

/* Responsive design */
@media (max-width: 1024px) {
  .scroll-progress-indicator {
    right: 1.5rem;
  }
  
  .scroll-progress-container {
    padding: 1rem 0.625rem;
    gap: 0.625rem;
  }
  
  .scroll-progress-circle {
    width: 0.875rem;
    height: 0.875rem;
  }
}

@media (max-width: 767px) {
  .scroll-progress-indicator {
    top: auto;
    bottom: 2rem;
    right: 50%;
    transform: translateX(50%);
  }
  
  .scroll-progress-container {
    flex-direction: row;
    padding: 0.75rem 1.25rem;
    gap: 0.75rem;
    border-radius: 2rem;
  }
  
  .scroll-progress-circle {
    width: 0.75rem;
    height: 0.75rem;
  }
}

/* Animation for smooth transitions */
@keyframes progressFill {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.scroll-progress-circle.active .scroll-progress-circle-inner {
  animation: progressFill 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Accessibility improvements */

/* Tooltip styling for section titles */
.scroll-progress-circle::before {
  content: attr(title);
  position: absolute;
  right: calc(100% + 1rem);
  top: 50%;
  transform: translateY(-50%);
  background: rgba(150, 111, 51, 0.95);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-family: "Source Sans Pro", sans-serif;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1001;
}

.scroll-progress-circle:hover::before {
  opacity: 1;
}

/* Hide tooltips on mobile */
@media (max-width: 767px) {
  .scroll-progress-circle::before {
    display: none;
  }
}
