.footer {
  background-color: #FFF8E4;
  color: #e5cca4;
  padding: 60px 0 30px 0;
  
}

.footer__content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 67px;
}

.footer__main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  gap: 80px;
}

.footer__brand-section {
  flex: 1;
  max-width: 400px;
}

.footer__brand {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.footer__logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.footer__logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
}

.footer__brand-text {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 28px;
  letter-spacing: -0.05em;
  line-height: 32px;
  color: #966F33;
}

.footer__description {
  font-family: '<PERSON><PERSON>', sans-serif;
  font-style: italic;
  font-size: 16px;
  line-height: 22px;
  color: #000000;
  margin: 0 0 28px 0;
  max-width: 350px;
}

/* CTA Button Section */
.footer__cta-section {
  margin-top: 8px;
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.footer__cta-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #966f33 0%, #8a6330 100%);
  border: 2px solid #966f33;
  border-radius: 0; /* Sharp rectangular corners as per user preference */
  cursor: pointer;
  font-family: 'Merriweather', serif;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.02em;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 280px;
  max-width: 350px;
  box-shadow:
    0 4px 16px rgba(150, 111, 51, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  animation: subtlePulse 3s ease-in-out infinite;
}

@keyframes subtlePulse {
  0%, 100% {
    box-shadow:
      0 4px 16px rgba(150, 111, 51, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      0 6px 20px rgba(150, 111, 51, 0.35),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
}

.footer__cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.footer__cta-button:hover::before {
  left: 100%;
}

.footer__cta-button:hover {
  background: linear-gradient(135deg, #a67a3a 0%, #966f33 100%);
  border-color: #d4af37;
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(150, 111, 51, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.footer__cta-button:active {
  transform: translateY(-1px);
  box-shadow:
    0 4px 16px rgba(150, 111, 51, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.footer__cta-text {
  flex: 1;
  text-align: left;
  line-height: 1.4;
}

.footer__cta-arrow {
  font-size: 18px;
  font-weight: 700;
  transition: transform 0.3s ease;
  color: #d4af37;
}

.footer__cta-button:hover .footer__cta-arrow {
  transform: translateX(4px);
  color: #ffffff;
}

/* Focus state for accessibility */
.footer__cta-button:focus {
  outline: 2px solid #d4af37;
  outline-offset: 2px;
}

/* Touch-friendly interactions for mobile */
@media (hover: none) and (pointer: coarse) {
  .footer__cta-button:hover {
    background: linear-gradient(135deg, #966f33 0%, #8a6330 100%);
    transform: none;
  }

  .footer__cta-button:active {
    background: linear-gradient(135deg, #8a6330 0%, #7a5a2d 100%);
    transform: scale(0.98);
  }
}

/* Disabled state (if needed) */
.footer__cta-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.footer__cta-button:disabled:hover {
  background: linear-gradient(135deg, #966f33 0%, #8a6330 100%);
  border-color: #966f33;
  transform: none;
}

.footer__connect-section {
  flex-shrink: 0;
  text-align: right;
}

.footer__connect-title {
  font-family: 'Battambang', sans-serif;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: -0.02em;
  color: #966F33;
  margin: 0 0 20px 0;
}

.footer__contact {
  margin-bottom: 24px;
}

.footer__email {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 16px;
  line-height: 20px;
  color: #b8a082;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer__email:hover {
  color: #e5cca4;
}

.footer__social {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}

.footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  color: #b8a082;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.footer__social-link:hover {
  color: #e5cca4;
  transform: translateY(-2px);
}

.footer__bottom {
  border-top: 1px solid rgba(229, 204, 164, 0.2);
  padding-top: 24px;
  text-align: left;
}

.footer__copyright {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 14px;
  line-height: 18px;
  color: #8a7a5e;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer {
    padding: 40px 0 20px 0;
    
  }

  .footer__content {
    padding: 0 20px;
  }

  .footer__main {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }

  .footer__brand-section {
    max-width: none;
  }

  .footer__brand {
    justify-content: center;
  }

  .footer__description {
    max-width: none;
    margin-bottom: 24px;
  }

  .footer__cta-section {
    margin-top: 0;
    display: flex;
    justify-content: center;
    margin-bottom: 32px;
  }

  .footer__cta-button {
    min-width: 280px;
    max-width: 320px;
    width: 100%;
    padding: 18px 24px;
    font-size: 15px;
    letter-spacing: 0.01em;
  }

  .footer__cta-text {
    text-align: center;
  }

  .footer__connect-section {
    text-align: center;
  }

  .footer__social {
    justify-content: center;
  }

  .footer__bottom {
    text-align: center;
    padding-top: 20px;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 30px 0 15px 0;
    
  }

  .footer__content {
    padding: 0 15px;
  }

  .footer__main {
    gap: 30px;
  }

  .footer__brand-text {
    font-size: 24px;
    line-height: 28px;
  }

  .footer__description {
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 20px;
  }

  .footer__cta-section {
    margin-bottom: 28px;
  }

  .footer__cta-button {
    min-width: 260px;
    max-width: 300px;
    padding: 16px 20px;
    font-size: 14px;
    letter-spacing: 0.01em;
    min-height: 56px; /* Ensure proper touch target size */
  }

  .footer__cta-text {
    line-height: 1.3;
  }

  .footer__cta-arrow {
    font-size: 16px;
  }

  .footer__connect-title {
    font-size: 18px;
  }

  .footer__email {
    font-size: 14px;
  }

  .footer__social {
    gap: 12px;
  }

  .footer__social-link {
    width: 40px;
    height: 40px;
  }

  .footer__copyright {
    font-size: 12px;
    line-height: 16px;
  }
}


